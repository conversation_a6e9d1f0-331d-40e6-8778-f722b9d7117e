import 'package:bloc/bloc.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import '../../models/last_activity_model.dart';
import '../../datasources/last_activity_remote_datasource.dart';

part 'last_activity_event.dart';
part 'last_activity_state.dart';

class LastActivityBloc extends Bloc<LastActivityEvent, LastActivityState>
    with ErrorHandlerMixin<LastActivityEvent, LastActivityState> {
  final NetworkInfo networkInfo;
  final LastActivityRemoteDatasourceImpl remoteDatasource;

  LastActivityBloc({
    required this.networkInfo,
    required this.remoteDatasource,
  }) : super(const LastActivityState()) {
    on<LoadLastActivity>(_onLoadLastActivity);
    on<RefreshLastActivity>(_onRefreshLastActivity);
    on<ClearLastActivity>(_onClearLastActivity);
  }

  Future<void> _onLoadLastActivity(
    LoadLastActivity event,
    Emitter<LastActivityState> emit,
  ) async {
    emit(state.copyWith(status: LastActivityStatus.loading));

    try {
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          status: LastActivityStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      final response = await remoteDatasource.fetchLastActivity(
        blockId: event.blockId,
      );

      if (response.success) {
        emit(state.copyWith(
          status: LastActivityStatus.success,
          activity: response.data,
          message: null,
        ));
      } else {
        emit(state.copyWith(
          status: LastActivityStatus.failure,
          message: response.message ?? LocaleKeys.nazoratchi_tuzilma_last_activity_error_occurred.tr(),
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        status: LastActivityStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: LastActivityStatus.failure,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  Future<void> _onRefreshLastActivity(
    RefreshLastActivity event,
    Emitter<LastActivityState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));

    try {
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          isRefreshing: false,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      final response = await remoteDatasource.fetchLastActivity(
        blockId: event.blockId,
      );

      if (response.success) {
        emit(state.copyWith(
          status: LastActivityStatus.success,
          activity: response.data,
          isRefreshing: false,
          message: null,
        ));
      } else {
        emit(state.copyWith(
          isRefreshing: false,
          message: response.message ?? LocaleKeys.nazoratchi_tuzilma_last_activity_error_occurred.tr(),
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        isRefreshing: false,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        isRefreshing: false,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  void _onClearLastActivity(
    ClearLastActivity event,
    Emitter<LastActivityState> emit,
  ) {
    emit(const LastActivityState());
  }

  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }
}
