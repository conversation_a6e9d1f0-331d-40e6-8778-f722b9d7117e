import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/widgets/last_activity_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../translations/locale_keys.g.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../../../../core/widgets/unified_shimmer.dart';
import '../models/rastalar_model.dart';

/// Widget that displays the status legend for page grid
class RastalarStatusLegend extends StatelessWidget {
  final int? rastalarCount;
  final String? blockId;
  final bool isLoading;

  const RastalarStatusLegend({
    super.key,
    required this.rastalarCount,
    this.isLoading = false, this.blockId,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildShimmerSkeleton(context);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          width: MediaQuery.of(context).size.width,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.surface,
            border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3), width: 1),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.market_structure_total_places.tr(),
                style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 14,
                    fontWeight: FontWeight.w400),
              ),
              Text(
                rastalarCount.toString(),
                style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 24,
                    fontWeight: FontWeight.w500),
              )
            ],
          ),
        ),
        LastActivityWidget(blockId: blockId),
        // Legend items in a 2x2 grid
        Row(
          children: [
            Expanded(
              child: _buildLegendItem(
                context: context,
                color: AppColors.cGreenishColor,
                label: LocaleKeys.market_structure_legend_paid.tr(),
                status: SquareStatus.paid,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildLegendItem(
                context: context,
                color: AppColors.cReddishColor,
                label: LocaleKeys.market_structure_legend_unpaid.tr(),
                status: SquareStatus.unpaid,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.w),
        Row(
          children: [
            Expanded(
              child: _buildLegendItem(
                context: context,
                color: AppColors.cYellowishColor,
                label: LocaleKeys.market_structure_legend_empty.tr(),
                status: SquareStatus.available,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildLegendItem(
                context: context,
                color: AppColors.cUnbindedColor,
                label: LocaleKeys.market_structure_legend_unassigned.tr(),
                status: SquareStatus.unbinded,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build shimmer skeleton using unified shimmer for better light theme support
  Widget _buildShimmerSkeleton(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Jami rastalar shimmer card with proper alignment
        ShimmerCard(
          width: MediaQuery.of(context).size.width,
          height: 60,
          borderRadius: 8,
          margin: EdgeInsets.zero,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12), // Match actual padding
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center, // Center align vertically
              children: [
                UnifiedShimmer.text(
                  context: context,
                  width: 85, // Match "Jami rastalar" text width
                  height: 14,
                ),
                UnifiedShimmer.text(
                  context: context,
                  width: 45, // Match number width
                  height: 24,
                ),
              ],
            ),
          ],
        ),
        Gap(20),
        // Legend items skeleton in a 2x2 grid
        Row(
          children: [
            Expanded(child: _buildShimmerLegendItem(context)),
            SizedBox(width: 16.w),
            Expanded(child: _buildShimmerLegendItem(context)),
          ],
        ),
        SizedBox(height: 12.w),
        Row(
          children: [
            Expanded(child: _buildShimmerLegendItem(context)),
            SizedBox(width: 16.w),
            Expanded(child: _buildShimmerLegendItem(context)),
          ],
        ),
      ],
    );
  }

  /// Build shimmer legend item skeleton using unified shimmer
  Widget _buildShimmerLegendItem(BuildContext context) {
    return Row(
      children: [
        // Color indicator skeleton
        UnifiedShimmer.container(
          context: context,
          width: 16.h,
          height: 16.h,
          borderRadius: 4,
        ),
        const SizedBox(width: 8),
        // Label skeleton
        Expanded(
          child: UnifiedShimmer.text(
            context: context,
            width: double.infinity,
            height: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required BuildContext context,
    required Color color,
    required String label,
    required SquareStatus status,
  }) {
    return Row(
      children: [
        // Color indicator
        Container(
          width: 16.h,
          height: 16.h,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        // Label
        Expanded(
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
