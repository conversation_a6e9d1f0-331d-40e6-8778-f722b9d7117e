import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../translations/locale_keys.g.dart';
import '../bloc/last_activity/last_activity_bloc.dart';
import '../models/last_activity_model.dart';

class LastActivityWidget extends StatelessWidget {
  final String? blockId;

  const LastActivityWidget({
    super.key,
    required this.blockId,
  });

  @override
  Widget build(BuildContext context) {
    if (blockId == null || blockId!.isEmpty) {
      return const SizedBox.shrink();
    }

    return BlocProvider(
      create: (context) => GetIt.instance<LastActivityBloc>()
        ..add(LoadLastActivity(blockId: blockId!)),
      child: _LastActivityContent(blockId: blockId!),
    );
  }
}

class _LastActivityContent extends StatefulWidget {
  final String blockId;

  const _LastActivityContent({required this.blockId});

  @override
  State<_LastActivityContent> createState() => _LastActivityContentState();
}

class _LastActivityContentState extends State<_LastActivityContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LastActivityBloc, LastActivityState>(
      builder: (context, state) {
        if (state.isLoading) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: _buildLoadingWidget(context),
            ),
          );
        }

        if (state.hasError) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
              child: _buildErrorWidget(
                  context, widget.blockId, state.message ?? 'Error occurred'),
            ),
          );
        }

        if (state.shouldShow && state.activity != null) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: _buildActivityWidget(context, state.activity!),
            ),
          );
        }

        // Hide widget with fade out animation
        _animationController.reverse();
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: const SizedBox.shrink(),
          ),
        );
      },
    );
  }

  Widget _buildActivityWidget(BuildContext context, ActivityRecord activity) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final primaryColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: primaryColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: primaryColor.withOpacity(0.6),
            width: 1,
          )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: isDark ? onPrimaryColor : primaryColor,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                LocaleKeys.nazoratchi_tuzilma_last_activity_title.tr(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isDark ? onPrimaryColor : primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            activity.message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDark ? onPrimaryColor : primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),

          ...[
            SizedBox(height: 4.h),
            Text(
              DateFormat('dd.MM.yyyy HH:mm')
                  .format(activity.timestamp.toLocal()),
              style: AppTextStyles.bodySmall.copyWith(
                color:
                    (isDark ? onPrimaryColor : primaryColor).withOpacity(0.8),
                fontSize: 12.sp,
              ),
            ),
          ],

          ///Time ago message
          // if (activity.timeAgo.isNotEmpty) ...[
          //   SizedBox(height: 4.h),
          //   Text(
          //     activity.timeAgo,
          //     style: AppTextStyles.bodySmall.copyWith(
          //       color: AppColors.white.withOpacity(0.8),
          //       fontSize: 12.sp,
          //     ),
          //   ),
          // ],
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final primaryColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    return Container(
      padding: EdgeInsets.all(25.w),
      decoration: BoxDecoration(
          color: primaryColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: primaryColor.withOpacity(0.6),
            width: 1,
          )),
      child: Row(
        children: [
          SizedBox(
            width: 16.w,
            height: 16.w,
            child: CircularProgressIndicator(
              color: isDark ? onPrimaryColor : primaryColor,
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            LocaleKeys.nazoratchi_tuzilma_last_activity_loading.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDark ? onPrimaryColor : primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(
      BuildContext context, String blockId, String message) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final errorColor = theme.colorScheme.error;
    final onErrorColor = theme.colorScheme.onError;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color:
            isDark ? errorColor.withOpacity(0.2) : errorColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12.r),
        border: isDark
            ? Border.all(
                color: errorColor.withOpacity(0.6),
                width: 1,
              )
            : null,
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: isDark ? errorColor : onErrorColor,
            size: 16.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              LocaleKeys.nazoratchi_tuzilma_last_activity_no_data.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: isDark ? errorColor : onErrorColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              context.read<LastActivityBloc>().add(
                    RefreshLastActivity(blockId: blockId),
                  );
            },
            child: Icon(
              Icons.refresh,
              color: isDark ? errorColor : onErrorColor,
              size: 18.sp,
            ),
          ),
        ],
      ),
    );
  }
}
