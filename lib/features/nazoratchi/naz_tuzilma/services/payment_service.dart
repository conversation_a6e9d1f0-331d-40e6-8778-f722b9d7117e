import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/services/error_handler_service.dart';
import '../../../../core/services/dio_error_handler.dart';
import '../../../../core/utils/api_path.dart';
import '../../../../translations/locale_keys.g.dart';
import '../models/payment_models.dart';

/// Service for handling payment-related API calls
class PaymentService {
  final Dio _dio;

  PaymentService({required Dio dio}) : _dio = dio;

  /// Base URL for payment API
  static const String _baseUrl = ApiPath.baseUrl + ApiPath.paymentPath;

  /// Create a new payment
  ///
  /// [seller] - Seller ID
  /// [days] - Number of days (e.g., 1, 2, 3)
  /// [price] - Payment amount
  /// [paymentType] - Payment type (1=cash, 2=terminal, 3=qr)
  /// [places] - List of place IDs
  Future<ApiResult<CreatePaymentResponse>> createPayment({
    required String seller,
    required int days,
    required int price,
    required PaymentType paymentType,
    required List<String> places,
  }) async {
    try {
      // Use the number of days directly

      final request = CreatePaymentRequest(
        seller: seller,
        day: days,
        price: price,
        paymentType: paymentType.value,
        places: places,
      );

      debugPrint('Creating payment with data: ${request.toJson()}');

      final response = await _dio.post(
        _baseUrl,
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      debugPrint('Payment creation response: ${response.data}');

      if (response.statusCode == 200) {
        // 200: New payment created successfully
        // Extract payment data from "message" field if it exists
        final paymentData = response.data is Map<String, dynamic> &&
                response.data.containsKey('message')
            ? response.data['message']
            : response.data;

        return ApiResult.success(
          CreatePaymentResponse.fromJson(paymentData),
          statusCode: 200,
        );
      } else if (response.statusCode == 201) {
        // 201: Pending payment exists (same model structure)
        // Extract payment data from "message" field if it exists
        final paymentData = response.data is Map<String, dynamic> &&
                response.data.containsKey('message')
            ? response.data['message']
            : response.data;

        return ApiResult.success(
          CreatePaymentResponse.fromJson(paymentData),
          statusCode: 201,
        );
      } else {
        return ApiResult.failure(
          errorMessage: 'Failed to create payment: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint(
          'DioException during payment creation: ${e.response?.statusCode}');
      debugPrint('Response data: ${e.response?.data}');

      // Handle as normal error (no special 400 handling)
      final errorMessage = DioErrorHandler.handleDioError(e);
      return ApiResult.failure(
        errorMessage: errorMessage,
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      debugPrint('General exception during payment creation: $e');
      return ApiResult.failure(
        errorMessage: 'Unexpected error during payment creation: $e',
      );
    }
  }

  /// Check payment status
  ///
  /// [paymentId] - Payment ID to check
  Future<ApiResult<PaymentStatusResponse>> checkPaymentStatus(
    String paymentId,
  ) async {
    return await ErrorHandlerService.executeApiCall<PaymentStatusResponse>(
      () async {
        debugPrint('Checking payment status for ID: $paymentId');

        final response = await _dio.get(
          '$_baseUrl/check/$paymentId',
          options: Options(
            headers: {
              'Accept': 'application/json',
            },
          ),
        );

        debugPrint('Payment status response: ${response.data}');

        if (response.statusCode == 200) {
          return PaymentStatusResponse.fromJson(response.data);
        } else {
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            type: DioExceptionType.badResponse,
            message: 'Failed to check payment status',
          );
        }
      },
      showToast: false, // We'll handle UI feedback in the calling widget
    );
  }

  /// Confirm payment (for cash and terminal payments)
  ///
  /// [paymentId] - Payment ID to confirm
  Future<ApiResult<ConfirmPaymentResponse>> confirmPayment(
    String paymentId,
  ) async {
    return await ErrorHandlerService.executeApiCall<ConfirmPaymentResponse>(
      () async {
        debugPrint('Confirming payment for ID: $paymentId');

        final response = await _dio.put(
          '$_baseUrl/check/$paymentId',
          options: Options(
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
        );

        debugPrint('Payment confirmation response: ${response.data}');

        if (response.statusCode == 200) {
          return ConfirmPaymentResponse.fromJson(response.data);
        } else {
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            type: DioExceptionType.badResponse,
            message: 'Failed to confirm payment',
          );
        }
      },
      showToast: false, // We'll handle UI feedback in the calling widget
    );
  }

  /// Handle payment error and convert to user-friendly message
  String _handlePaymentError(dynamic error) {
    if (error is DioException) {
      switch (error.response?.statusCode) {
        case 400:
          return LocaleKeys.payment_errors_bad_request.tr();
        case 401:
          return LocaleKeys.payment_errors_unauthorized.tr();
        case 404:
          return LocaleKeys.payment_errors_not_found.tr();
        case 409:
          return LocaleKeys.payment_errors_conflict.tr();
        case 500:
          return LocaleKeys.payment_errors_server_error.tr();
        default:
          return LocaleKeys.payment_errors_payment_error.tr();
      }
    }
    return LocaleKeys.payment_errors_unexpected_error.tr();
  }

  /// Get pending payments for current user
  /// Returns list of unconfirmed payments that need to be confirmed or cancelled
  Future<ApiResult<List<ConfirmPaymentResponse>>> getPendingPayments() async {
    return await ErrorHandlerService.executeApiCall<
        List<ConfirmPaymentResponse>>(
      () async {
        final response = await _dio.get('$_baseUrl/pending');
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data
            .map((json) => ConfirmPaymentResponse.fromJson(json))
            .toList();
      },
    );
  }

  /// Cancel a pending payment
  ///
  /// [paymentId] - Payment ID to cancel
  Future<ApiResult<void>> cancelPayment(String paymentId) async {
    return await ErrorHandlerService.executeApiCall<void>(
      () async {
        await _dio.delete('$_baseUrl/$paymentId');
      },
    );
  }

  /// Delete an old cheque
  ///
  /// [chequeId] - Cheque ID to delete
  Future<ApiResult<void>> deleteOldCheque(int chequeId) async {
    return await ErrorHandlerService.executeApiCall<void>(
      () async {
        debugPrint('Deleting old cheque with ID: $chequeId');

        final response = await _dio.delete(
          '$_baseUrl/delete/$chequeId',
          options: Options(
            headers: {
              'Accept': 'application/json',
            },
          ),
        );

        debugPrint('Delete cheque response: ${response.statusCode}');
      },
    );
  }

  /// Check place verification status (Tekshirildi functionality)
  ///
  /// [sellerId] - Seller ID to check
  Future<ApiResult<PlaceCheckResponse>> checkPlace(String sellerId) async {
    try {
      final request = PlaceCheckRequest(seller: sellerId);

      debugPrint('Checking place for seller: $sellerId');

      final response = await _dio.post(ApiPath.baseUrl + ApiPath.placeCheckPath,
          data: request.toJson());

      debugPrint('Place check response: ${response.data}');

      if (response.statusCode == 200) {
        return ApiResult.success(
          PlaceCheckResponse.fromJson(response.data),
          statusCode: 200,
        );
      } else {
        return ApiResult.failure(
          errorMessage: 'Place check failed: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint('DioException during place check: ${e.response?.statusCode}');
      debugPrint('Response data: ${e.response?.data}');

      final errorMessage = DioErrorHandler.handleDioError(e);
      return ApiResult.failure(
        errorMessage: errorMessage,
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      debugPrint('General exception during place check: $e');
      return ApiResult.failure(
        errorMessage: 'Unexpected error during place check: $e',
      );
    }
  }
}

/// Extension to get payment type description with localization
extension PaymentTypeExtension on PaymentType {
  String get description {
    switch (this) {
      case PaymentType.cash:
        return LocaleKeys.payment_types_cash.tr();
      case PaymentType.terminal:
        return LocaleKeys.payment_types_terminal.tr();
      case PaymentType.qr:
        return LocaleKeys.payment_types_qr.tr();
    }
  }

  String get shortDescription {
    switch (this) {
      case PaymentType.cash:
        return LocaleKeys.payment_types_cash_short.tr();
      case PaymentType.terminal:
        return LocaleKeys.payment_types_terminal_short.tr();
      case PaymentType.qr:
        return LocaleKeys.payment_types_qr_short.tr();
    }
  }
}
