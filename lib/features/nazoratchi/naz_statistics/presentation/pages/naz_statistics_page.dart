import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/utils/app_functions.dart';
import 'package:click_bazaar/core/widgets/circle_button.dart';
import 'package:click_bazaar/core/widgets/universal_loading.dart';
import 'package:click_bazaar/core/widgets/rest_schedule_date_picker.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/presentation/widgets/statistics_card.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:click_bazaar/di/dependency_injection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:intl/intl.dart';
import '../bloc/naz_statistics_bloc/naz_statistics_bloc.dart';
import '../widgets/statistics_circle_chart.dart';
import '../widgets/statistics_legend.dart';
import '../widgets/statistics_metrics_row.dart';
import '../../../../../translations/locale_keys.g.dart';

/// Statistics page with BLoC integration
/// This demonstrates how to use the NazStatisticsBloc with the existing UI
class NazStatisticsPage extends StatefulWidget {
  const NazStatisticsPage({super.key});

  @override
  State<NazStatisticsPage> createState() => _NazStatisticsPageState();
}

class _NazStatisticsPageState extends State<NazStatisticsPage> {
  DateTime _selectedDate = DateTime.now();
  DateFormat dateFormat = DateFormat('yyyy-MM-dd');

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<NazStatisticsBloc>()
        ..add(LoadAllStatisticsEvent(date: _formatDate(_selectedDate))),
      child: Builder(
        builder: (context) {
          return Scaffold(
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              appBar: AppBar(
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                elevation: 0,
                centerTitle: false,
                title: Text(
                  LocaleKeys.navigation_statistics.tr(),
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: CircleButton(
                      iconHeight: 24,
                      iconWidth: 24,
                      onPressed: () {},
                      svgPath: Assets.iconsBell,
                    ),
                  )
                ],
              ),
              body: Column(
                children: [
                  // Fixed section - RestScheduleDatePicker
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.fromLTRB(16, 4, 16, 0),
                    child: RestScheduleDatePicker(
                      selectedDate: _selectedDate,
                      onDateSelected: (date) {
                        setState(() {
                          _selectedDate = date;
                        });
                        // Load statistics for the new date
                        context.read<NazStatisticsBloc>().add(
                              LoadAllStatisticsEvent(date: _formatDate(date)),
                            );
                      },
                    ),
                  ),
                  // Scrollable content section
                  Expanded(
                    child: BlocConsumer<NazStatisticsBloc, NazStatisticsState>(
                      listener: (context, state) {
                        // Handle refresh errors with toast messages
                        // Show toast when there's a refresh error and existing data
                        if (state.hasRefreshError &&
                            state.hasAnyStatistics &&
                            state.message != null) {
                          // This is a refresh error - show toast while keeping existing content
                          final message = state.isNetworkError
                              ? LocaleKeys.common_no_internet_connection.tr()
                              : state.message!;

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(message, style: TextStyle(color: Theme.of(context).colorScheme.onError)),
                              backgroundColor: AppColors.cReddishColor,
                              duration: const Duration(seconds: 3),
                              action: state.isNetworkError
                                  ? SnackBarAction(
                                      label: LocaleKeys.common_retry.tr(),
                                      textColor: AppColors.white,
                                      onPressed: () {
                                        context.read<NazStatisticsBloc>().add(
                                              RefreshAllStatisticsEvent(
                                                  date: _formatDate(
                                                      _selectedDate)),
                                            );
                                      },
                                    )
                                  : null,
                            ),
                          );
                        }
                      },
                      builder: (context, state) {
                        if (state.isLoading ||
                            state.isInitial ||
                            state.isRefreshing) {
                          return UniversalLoading.shimmer(
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 12),
                                  // Statistics metrics row skeleton
                                  UniversalLoading.statisticsMetricsRow(),
                                  const SizedBox(height: 8),
                                  // Circle chart skeleton
                                  UniversalLoading.statisticsCircleChart(),
                                  const SizedBox(height: 8),
                                  // Payment cards skeleton
                                  UniversalLoading.statisticsPaymentCards(),
                                ],
                              ),
                            ),
                          );
                        }
                        // Handle initial load errors - show centered error widget only if no existing data
                        if (state.isFailure && !state.hasAnyStatistics) {
                          return SizedBox(
                            height: MediaQuery.of(context).size.height * 0.6,
                            child: Center(
                              child: UniversalLoading.error(
                                message: state.isNetworkError
                                    ? LocaleKeys.common_no_internet_connection
                                        .tr()
                                    : (state.message ??
                                        LocaleKeys.common_error_occurred.tr()),
                                onRetry: () {
                                  context.read<NazStatisticsBloc>().add(
                                        LoadAllStatisticsEvent(
                                            date: _formatDate(_selectedDate)),
                                      );
                                },
                                retryButtonText: LocaleKeys.common_retry.tr(),
                                icon: state.isNetworkError
                                    ? Icons.wifi_off
                                    : null,
                              ),
                            ),
                          );
                        }

                        return RefreshIndicator(
                          onRefresh: () async {
                            // Add a small delay to show the refresh indicator
                            await Future.delayed(
                                const Duration(milliseconds: 300));
                            context.read<NazStatisticsBloc>().add(
                                  RefreshAllStatisticsEvent(
                                      date: _formatDate(_selectedDate)),
                                );
                          },
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 12),
                                // Keep the original StatisticsMetricsRow design
                                StatisticsMetricsRow(
                                  rejda: state.planStatistics?.rejda,
                                  tushgan: state.planStatistics?.tushgan,
                                  clickOrqali:
                                      state.planStatistics?.clickOrqali,
                                  naqdPul: state.planStatistics?.naqdPul,
                                  terminalOrqali:
                                      state.planStatistics?.terminalOrqali,
                                  qarzdalik: state.planStatistics?.qarzdalik,
                                  date: dateFormat.format(_selectedDate),
                                ),
                                const SizedBox(height: 12),
                                // Place Statistics Chart with real data
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      StatisticsCircleChart(
                                        totalCount:
                                            state.placeStatistics?.totalCount ??
                                                0,
                                        tolangan:
                                            state.placeStatistics?.tolangan ??
                                                0,
                                        tolanmagan:
                                            state.placeStatistics?.tolanmagan ??
                                                0,
                                        bosh: state.placeStatistics?.bosh ?? 0,
                                        belgilanmaganCount: state
                                                .placeStatistics
                                                ?.belgilanmaganCount ??
                                            0,
                                      ),
                                      const SizedBox(height: 32),
                                      StatisticsLegend(
                                        successCount:
                                            state.placeStatistics?.tolangan ??
                                                0,
                                        failureCount:
                                            state.placeStatistics?.tolanmagan ??
                                                0,
                                        bothCount:
                                            state.placeStatistics?.bosh ?? 0,
                                        belgilanmaganCount: state
                                                .placeStatistics
                                                ?.belgilanmaganCount ??
                                            0,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 12),
                                // Payment Statistics Cards with real data
                                Row(
                                  children: [
                                    StatisticsMetricCard(
                                      title: LocaleKeys
                                          .nazoratchi_statistics_payment_delivered
                                          .tr(),
                                      value: AppFunctions.formatNumberSafe(state
                                          .paymentStatistics?.topshirilgan),
                                      backgroundColor: AppColors.cGreenishColor
                                          .withValues(alpha: 0.1),
                                      mainTextColor: AppColors.cGreenishColor,
                                      onTap: () {},
                                    ),
                                    const SizedBox(width: 12),
                                    StatisticsMetricCard(
                                      title: LocaleKeys
                                          .nazoratchi_statistics_payment_not_delivered
                                          .tr(),
                                      value: AppFunctions.formatNumberSafe(state
                                          .paymentStatistics?.topshirilmagan),
                                      backgroundColor: AppColors.cReddishColor
                                          .withValues(alpha: 0.1),
                                      mainTextColor: AppColors.cReddishColor,
                                      onTap: () {},
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ));
        },
      ),
    );
  }

  /// Format date to YYYY-MM-DD string
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Format payment value with comma separators
  String _formatPaymentValue(int? value) {
    final double result = (value ?? 0) / 10.0;
    if (result == result.toInt()) {
      return AppFunctions.formatNumber(result.toInt());
    } else {
      // For decimal values, format the number and add decimal part
      final intPart = result.floor();
      final decimalPart = ((result - intPart) * 10).round();
      return '${AppFunctions.formatNumber(intPart)}.${decimalPart}';
    }
  }
}
