import 'dart:io';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/features/face_control/bio_lock_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/utils/api_path.dart';
import '../../../../../core/utils/jwt_decoder.dart';
import '../../../../../core/widgets/role_switcher.dart';
import '../../../../../core/widgets/universal_avatar.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../core/widgets/animated_theme_switcher.dart';
import '../../../../../core/widgets/enhanced_theme_toggle.dart';
import '../../../../../core/widgets/theme_aware_scaffold.dart';
import '../../../../../core/services/logout_service.dart';
import '../../../../../generated/assets.dart';
import '../../../../../di/dependency_injection.dart' as di;
import '../../../../auth/services/auth_service.dart';
import '../../../../auth/models/user_profile_model.dart';
import '../../../../auth/presentation/pages/login_page.dart';
import '../bloc/naz_profile_bloc.dart';
import '../bloc/naz_profile_event.dart';
import '../bloc/naz_profile_state.dart';
import '../widgets/naz_profile_menu_item.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../../../../../core/services/language_service.dart';
import 'naz_personal_info_page.dart';

class NazProfilePage extends StatefulWidget {
  final Function(UserRole) onRoleChanged;

  const NazProfilePage({
    super.key,
    required this.onRoleChanged,
  });

  @override
  State<NazProfilePage> createState() => _NazProfilePageState();
}

class _NazProfilePageState extends State<NazProfilePage> {
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();
  String _selectedLanguage = LanguageService.getCurrentLanguage();
  late final AuthService _authService;
  late final NazProfileBloc _nazProfileBloc;
  late final GetStorage _storage;
  UserProfile? _userProfile;
  String? _cacheBustingTimestamp;

  @override
  void initState() {
    super.initState();
    _authService = AuthService(
      dio: di.di(),
      storage: di.di(),
      networkInfo: di.di(),
    );
    _nazProfileBloc = di.di<NazProfileBloc>();
    _storage = di.di<GetStorage>();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _nazProfileBloc.close();
    super.dispose();
  }

  /// Load user profile from cache or API
  void _loadUserProfile({bool forceRefresh = false}) {
    final token = _storage.read(TOKEN);
    final isDemoMode = _storage.read(is_demo) ?? false;

    String? userId;

    if (token != null) {
      // Regular authenticated user
      userId = JwtDecoder.getUserId(token);
    } else if (isDemoMode) {
      // Demo mode - extract user ID from guest token
      userId = JwtDecoder.getUserId(GUEST_TOKEN);
    }

    if (userId != null) {
      _nazProfileBloc.add(LoadNazProfileEvent(
        userId: userId,
        forceRefresh: forceRefresh,
      ));
    }
  }

  /// Handle refresh action
  Future<void> _onRefresh() async {
    _loadUserProfile(forceRefresh: true);

    // Wait for the refresh to complete
    await _nazProfileBloc.stream
        .firstWhere((state) => state is! NazProfileRefreshing);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _nazProfileBloc,
      child: ThemeAwareScaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: BlocListener<NazProfileBloc, NazProfileState>(
          listener: (context, state) {
            if (state is NazProfileLoaded) {
              if (mounted) {
                setState(() {
                  _userProfile = state.userProfile;
                  // Clear local image when profile is loaded (success or revert)
                  _profileImage = null;
                });
              }
            } else if (state is NazProfileImageUpdated) {
              // Show success message with mounted check
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message,
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onPrimary)),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                  ),
                );
              }
              // Set cache-busting timestamp to force image reload
              if (mounted) {
                setState(() {
                  _cacheBustingTimestamp =
                      DateTime.now().millisecondsSinceEpoch.toString();
                });
              }

              // Clear image cache to force reload
              if (state.updatedProfile.image != null) {
                final imageUrl =
                    '${ApiPath.baseUrlFile}${state.updatedProfile.image}';
                PaintingBinding.instance.imageCache
                    .evict(NetworkImage(imageUrl));
              }
              // Local image will be cleared when NazProfileLoaded is emitted next
            } else if (state is NazProfileError) {
              // Show error message with mounted check
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message,
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onError)),
                    backgroundColor: Theme.of(context).colorScheme.error,
                  ),
                );
              }
              // Local image will be cleared when NazProfileLoaded is emitted next
            }
          },
          child: RefreshIndicator(
            onRefresh: _onRefresh,
            color: Theme.of(context).colorScheme.primary,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  scrolledUnderElevation: 0,
                  surfaceTintColor: Colors.transparent,
                  centerTitle: false,
                  floating: false,
                  pinned: true,
                  expandedHeight: 0,
                  title: Text(
                    LocaleKeys.navigation_profile.tr(),
                    style: AppTextStyles.titleLarge.copyWith(
                      color: Theme.of(context).colorScheme.onBackground,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      _buildProfileHeader(),

                      ///For debugging
                      // Padding(
                      //   padding: EdgeInsets.symmetric(horizontal: 16.w),
                      //   child: RoleSwitcher(
                      //     currentRole: UserRole.nazoratchi,
                      //     onRoleChanged: widget.onRoleChanged,
                      //   ),
                      // ),
                      Gap(24.h),
                      _buildMenuSection(),
                      Gap(24.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Decorative semicircle background
        // Positioned(
        //   top: -720,
        //   left: -150,
        //   right: -150,
        //   child: Container(
        //     height: 1000,
        //     decoration: const BoxDecoration(
        //       color: Theme.of(context).colorScheme.surface,
        //       shape: BoxShape.circle,
        //     ),
        //   ),
        // ),
        // Profile content
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              // Profile Image with Camera Icon using UniversalAvatar
              BlocBuilder<NazProfileBloc, NazProfileState>(
                builder: (context, state) {
                  // Only show local image during upload process, otherwise use server image
                  File? displayImageFile;
                  String? displayImageUrl;

                  if (state is NazProfileImageUpdating &&
                      _profileImage != null) {
                    // Show local image only during upload
                    displayImageFile = _profileImage;
                    displayImageUrl = null;
                  } else if (state is NazProfileLoaded) {
                    // Show server image URL (updated or old)
                    displayImageFile = null;
                    String? baseImageUrl = state.userProfile.image;
                    if (baseImageUrl != null &&
                        _cacheBustingTimestamp != null) {
                      // Add cache-busting parameter to force reload
                      displayImageUrl =
                          '$baseImageUrl?t=$_cacheBustingTimestamp';
                    } else {
                      displayImageUrl = baseImageUrl;
                    }
                  } else if (state is NazProfileError &&
                      state.cachedProfile != null) {
                    // Show cached server image URL on error
                    displayImageFile = null;
                    displayImageUrl = state.cachedProfile!.image;
                  }

                  return UniversalAvatar.profile(
                    key: _cacheBustingTimestamp != null
                        ? ValueKey('avatar_$_cacheBustingTimestamp')
                        : null,
                    imageFile: displayImageFile,
                    imageUrl: displayImageUrl,
                    size: 120,
                    isLoading: state is NazProfileRefreshing ||
                        state is NazProfileImageUpdating,
                    onCameraTap: _showImagePickerOptions,
                    fallbackText: state is NazProfileLoaded
                        ? state.userProfile.fullNameWithMiddle
                        : state is NazProfileError &&
                                state.cachedProfile != null
                            ? state.cachedProfile!.fullNameWithMiddle
                            : null,
                  );
                },
              ),
              Gap(24.h),
              BlocBuilder<NazProfileBloc, NazProfileState>(
                builder: (context, state) {
                  if (state is NazProfileLoaded) {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            state.userProfile.fullNameWithMiddle,
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            state.userProfile.formattedPhone,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else if (state is NazProfileLoading ||
                      state is NazProfileRefreshing) {
                    return UniversalLoading.shimmer(
                      child: SizedBox(
                        child: Column(
                          children: [
                            Container(
                              height: 24.h,
                              width: 200.w,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).colorScheme.onBackground,
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                            Gap(8.h),
                            Container(
                              height: 16.h,
                              width: 150.w,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).colorScheme.onBackground,
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  } else if (state is NazProfileError &&
                      state.cachedProfile != null) {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            state.cachedProfile!.fullNameWithMiddle,
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            state.cachedProfile!.formattedPhone,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            LocaleKeys.profile_default_user_name.tr(),
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            LocaleKeys.profile_loading_info.tr(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
              Gap(32.h),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMenuSection() {
    return Column(
      children: [
        _buildDivider(),
        NazProfileMenuItem(
          title: LocaleKeys.profile_biometric_data.tr(),
          iconPath: Assets.iconsFaceId,
          doNotColorFilter: true,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BioLockPage(),
              ),
            );
          },
          subTitle: LocaleKeys.profile_biometric_data_subtitle.tr(),
        ),
        _buildDivider(),
        NazProfileMenuItem(
          title: LocaleKeys.profile_personal_info.tr(),
          iconPath: Assets.iconsCircleAvatar,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NazPersonalInfoPage(
                  userProfile: _userProfile,
                ),
              ),
            );
          },
          subTitle: LocaleKeys.profile_seller_personal_info_subtitle.tr(),
        ),
        // _buildDivider(),
        // NazProfileMenuItem(
        //   title: LocaleKeys.profile_notifications.tr(),
        //   iconPath: Assets.iconsBell,
        //   onTap: () {
        //     // Navigate to notifications settings
        //   },
        //   subTitle: LocaleKeys.profile_notifications_subtitle.tr(),
        // ),
        _buildDivider(),
        NazProfileMenuItem(
          title: LocaleKeys.profile_language_selection.tr(),
          iconPath: Assets.iconsGlobus,
          onTap: () {
            _showLanguageSelectionDialog();
          },
          subTitle: _selectedLanguage == 'uz'
              ? LocaleKeys.profile_language_subtitle_uzbek.tr()
              : LocaleKeys.profile_language_subtitle_russian.tr(),
        ),
        _buildDivider(),
        NazProfileMenuItem(
          title: LocaleKeys.sotuvchi_settings_theme_selection.tr(),
          iconPath: Assets.iconsDarkMode,
          onTap: () {
            showModalBottomSheet(
              context: context,
              backgroundColor: Colors.transparent,
              isScrollControlled: true,
              builder: (context) => const ThemeSelectionBottomSheet(),
            );
          },
          subTitle: LocaleKeys.sotuvchi_settings_theme_selection_subtitle.tr(),
          trailing: const SimpleThemeToggle(size: 24),
        ),
        _buildDivider(),
        NazProfileMenuItem(
          title: LocaleKeys.profile_logout.tr(),
          iconPath: Assets.iconsLogout,
          onTap: () {
            showLogoutDialog(context);
          },
          subTitle: LocaleKeys.profile_logout_subtitle.tr(),
          isLogout: true,
        ),
        _buildDivider(),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Theme.of(context).colorScheme.outline,
    );
  }

  void _showLanguageSelectionDialog() {
    String tempSelectedLanguage = _selectedLanguage;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 280.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with title
                    Container(
                      padding: EdgeInsets.fromLTRB(24.w, 20.h, 16.w, 16.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            LocaleKeys.language_dialog_title.tr(),
                            style: AppTextStyles.titleMedium.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 18.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Language options
                    Divider(
                      height: 1,
                      color: AppColors.cGrayBorderColor,
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
                      child: Column(
                        children: [
                          _buildLanguageOptionForDialog(
                            language: LocaleKeys.language_uzbek.tr(),
                            isSelected: tempSelectedLanguage == 'uz',
                            onTap: () async {
                              setDialogState(() {
                                tempSelectedLanguage = 'uz';
                              });
                              await LanguageService.setLanguage(context, 'uz');
                              if (mounted) {
                                setState(() {
                                  _selectedLanguage = 'uz';
                                });
                                Navigator.pop(context);
                              }
                            },
                          ),
                          Gap(10),
                          _buildLanguageOptionForDialog(
                            language: LocaleKeys.language_russian.tr(),
                            isSelected: tempSelectedLanguage == 'ru',
                            onTap: () async {
                              setDialogState(() {
                                tempSelectedLanguage = 'ru';
                              });
                              await LanguageService.setLanguage(context, 'ru');
                              if (mounted) {
                                setState(() {
                                  _selectedLanguage = 'ru';
                                });
                                Navigator.pop(context);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gap(20.h),
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.w,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageOptionForDialog({
    required String language,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  width: 2.w,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    )
                  : null,
            ),
            Gap(10),
            Expanded(
              child: Text(
                language,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onBackground,
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              LocaleKeys.profile_select_image.tr(),
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onBackground,
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(24.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImagePickerOption(
                  icon: Icons.camera_alt,
                  label: LocaleKeys.profile_camera.tr(),
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildImagePickerOption(
                  icon: Icons.photo_library,
                  label: LocaleKeys.profile_gallery.tr(),
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              ],
            ),
            Gap(24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 32.w,
            ),
            Gap(8.h),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onBackground,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        // Show local image temporarily during upload
        setState(() {
          _profileImage = File(image.path);
        });

        // Update profile image via API
        final token = _storage.read(TOKEN);
        if (token != null) {
          final userId = JwtDecoder.getUserId(token);
          if (userId != null) {
            _nazProfileBloc.add(UpdateNazProfileImageEvent(
              userId: userId,
              imagePath: image.path,
            ));
          } else {
            // If no userId, revert the local image
            setState(() {
              _profileImage = null;
            });
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(LocaleKeys.profile_user_data_not_found.tr(),
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.onError)),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }
        } else {
          // If no token, revert the local image
          setState(() {
            _profileImage = null;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(LocaleKeys.profile_authentication_error.tr(),
                    style: TextStyle(
                        color: Theme.of(context).colorScheme.onError)),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        }
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      // Revert local image on any error
      setState(() {
        _profileImage = null;
      });

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocaleKeys.profile_image_selection_error.tr(),
                style: TextStyle(color: Theme.of(context).colorScheme.onError)),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
